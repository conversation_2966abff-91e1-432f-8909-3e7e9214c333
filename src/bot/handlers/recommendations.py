"""
EduGuideBot v3 Recommendation Handlers
Handles recommendation actions and major details
"""

import logging
from telegram import Update, CallbackQuery
from telegram.ext import ContextTypes

from ..keyboards_v3 import create_major_details_keyboard
from ..telegram_safe_v3 import safe_answer_callback, safe_edit_message
from ...core.data.loader import get_major_details, get_university_details

logger = logging.getLogger(__name__)


async def handle_recommendation_action(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle recommendation action callbacks."""
    query = update.callback_query
    await safe_answer_callback(query)
    
    try:
        callback_data = query.data
        
        if callback_data == "back_to_menu":
            # Return to main menu
            welcome_text = (
                "🎓 សូមស្វាគមន៍មកកាន់ EduGuideBot v3!\n\n"
                "ខ្ញុំនឹងជួយអ្នកស្វែងរកសាកលវិទ្យាល័យ និងមុខជំនាញដែលសមស្របសម្រាប់អ្នក។\n\n"
                "សូមជ្រើសរើសភាសា:"
            )
            
            from ..keyboards_v3 import create_language_keyboard
            keyboard = create_language_keyboard()
            
            await safe_edit_message(query, welcome_text, keyboard)
            
        elif callback_data.startswith("major_"):
            # Show major details
            major_id = callback_data.split("_")[1]
            await show_major_details(query, major_id)
            
        elif callback_data.startswith("other_"):
            # Show other majors from same university
            university_id = callback_data.split("_")[1]
            await show_other_majors(query, university_id)
            
        elif callback_data.startswith("location_"):
            # Show university location
            university_id = callback_data.split("_")[1]
            await show_university_location(query, university_id)
            
        elif callback_data.startswith("contact_"):
            # Show contact information
            university_id = callback_data.split("_")[1]
            await show_contact_info(query, university_id)
            
    except Exception as e:
        logger.error(f"Error in handle_recommendation_action: {e}")
        await safe_edit_message(
            query,
            "❌ សូមអភ័យទោស! មានបញ្ហាក្នុងការបង្ហាញ។ សូមព្យាយាមម្តងទៀត។"
        )


async def handle_major_details(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle major details callback."""
    query = update.callback_query
    await safe_answer_callback(query)
    
    try:
        major_id = query.data.split("_")[1]
        await show_major_details(query, major_id)
        
    except Exception as e:
        logger.error(f"Error in handle_major_details: {e}")
        await safe_edit_message(
            query,
            "❌ សូមអភ័យទោស! មានបញ្ហាក្នុងការបង្ហាញ។ សូមព្យាយាមម្តងទៀត។"
        )


async def show_major_details(query: CallbackQuery, major_id: str) -> None:
    """Show detailed information about a major."""
    try:
        # Get major details
        major_details = await get_major_details(major_id)
        
        if not major_details:
            await safe_edit_message(
                query,
                "❌ រកមិនឃើញព័ត៌មានអំពីមុខជំនាញនេះ។"
            )
            return
        
        # Format details
        details_text = f"🎓 {major_details.get('major_name', 'មិនមានឈ្មោះ')}\n\n"
        details_text += f"🏫 {major_details.get('university_name', 'មិនមានឈ្មោះ')}\n"
        details_text += f"📍 {major_details.get('location', 'មិនមានទីតាំង')}\n"
        details_text += f"⏱️ រយៈពេល: {major_details.get('duration', 'N/A')} ឆ្នាំ\n"
        details_text += f"💰 ថ្លៃសិក្សា: {major_details.get('fees_usd', 'N/A')} USD/ឆ្នាំ\n"
        details_text += f"📊 អត្រាការងារ: {major_details.get('employment_rate', 'N/A')}%\n\n"
        
        if major_details.get('description'):
            details_text += f"📝 ការពិពណ៌នា:\n{major_details['description']}\n\n"
        
        if major_details.get('career_prospects'):
            details_text += f"🎯 ការងារអនាគត:\n{major_details['career_prospects']}\n\n"
        
        if major_details.get('internship_availability'):
            details_text += f"💼 កម្មសិក្សា: {major_details['internship_availability']}\n\n"
        
        if major_details.get('contact_info'):
            details_text += f"📞 ទំនាក់ទំនង: {major_details['contact_info']}"
        
        # Create action buttons
        keyboard = create_major_details_keyboard(
            major_id, 
            major_details.get('university_id', '')
        )
        
        await safe_edit_message(query, details_text, keyboard)
        
    except Exception as e:
        logger.error(f"Error in show_major_details: {e}")
        await safe_edit_message(
            query,
            "❌ សូមអភ័យទោស! មានបញ្ហាក្នុងការបង្ហាញ។ សូមព្យាយាមម្តងទៀត។"
        )


async def show_other_majors(query: CallbackQuery, university_id: str) -> None:
    """Show other majors from the same university."""
    try:
        # Get university details and majors
        university_details = await get_university_details(university_id)
        
        if not university_details:
            await safe_edit_message(
                query,
                "❌ រកមិនឃើញព័ត៌មានអំពីសាកលវិទ្យាល័យនេះ។"
            )
            return
        
        majors_text = f"🏫 មុខជំនាញនៅ {university_details.get('name', 'មិនមានឈ្មោះ')}\n\n"
        
        majors = university_details.get('majors', [])[:10]  # Show top 10
        for i, major in enumerate(majors, 1):
            majors_text += f"{i}. {major.get('name', 'មិនមានឈ្មោះ')}\n"
            majors_text += f"   💰 {major.get('fees_usd', 'N/A')} USD\n\n"
        
        # Back button
        from telegram import InlineKeyboardButton, InlineKeyboardMarkup
        keyboard = InlineKeyboardMarkup([
            [InlineKeyboardButton("🔙 ត្រឡប់ទៅបញ្ជី", callback_data="back_to_recommendations")]
        ])
        
        await safe_edit_message(query, majors_text, keyboard)
        
    except Exception as e:
        logger.error(f"Error in show_other_majors: {e}")
        await safe_edit_message(
            query,
            "❌ សូមអភ័យទោស! មានបញ្ហាក្នុងការបង្ហាញ។ សូមព្យាយាមម្តងទៀត។"
        )


async def show_university_location(query: CallbackQuery, university_id: str) -> None:
    """Show university location information."""
    try:
        university_details = await get_university_details(university_id)
        
        if not university_details:
            await safe_edit_message(
                query,
                "❌ រកមិនឃើញព័ត៌មានអំពីសាកលវិទ្យាល័យនេះ។"
            )
            return
        
        location_text = f"📍 ទីតាំង {university_details.get('name', 'មិនមានឈ្មោះ')}\n\n"
        location_text += f"🏢 អាសយដ្ឋាន: {university_details.get('address', 'មិនមានអាសយដ្ឋាន')}\n"
        location_text += f"🌍 ខេត្ត/ក្រុង: {university_details.get('city', 'មិនមានទីតាំង')}\n"
        
        if university_details.get('campus_info'):
            location_text += f"🏫 ព័ត៌មានបន្ថែម: {university_details['campus_info']}\n"
        
        # Back button
        from telegram import InlineKeyboardButton, InlineKeyboardMarkup
        keyboard = InlineKeyboardMarkup([
            [InlineKeyboardButton("🔙 ត្រឡប់ទៅបញ្ជី", callback_data="back_to_recommendations")]
        ])
        
        await safe_edit_message(query, location_text, keyboard)
        
    except Exception as e:
        logger.error(f"Error in show_university_location: {e}")
        await safe_edit_message(
            query,
            "❌ សូមអភ័យទោស! មានបញ្ហាក្នុងការបង្ហាញ។ សូមព្យាយាមម្តងទៀត។"
        )


async def show_contact_info(query: CallbackQuery, university_id: str) -> None:
    """Show university contact information."""
    try:
        university_details = await get_university_details(university_id)
        
        if not university_details:
            await safe_edit_message(
                query,
                "❌ រកមិនឃើញព័ត៌មានអំពីសាកលវិទ្យាល័យនេះ។"
            )
            return
        
        contact_text = f"📞 ទំនាក់ទំនង {university_details.get('name', 'មិនមានឈ្មោះ')}\n\n"
        
        if university_details.get('phone'):
            contact_text += f"📱 ទូរស័ព្ទ: {university_details['phone']}\n"
        
        if university_details.get('email'):
            contact_text += f"📧 អ៊ីមែល: {university_details['email']}\n"
        
        if university_details.get('website'):
            contact_text += f"🌐 គេហទំព័រ: {university_details['website']}\n"
        
        if university_details.get('facebook'):
            contact_text += f"📘 Facebook: {university_details['facebook']}\n"
        
        # Back button
        from telegram import InlineKeyboardButton, InlineKeyboardMarkup
        keyboard = InlineKeyboardMarkup([
            [InlineKeyboardButton("🔙 ត្រឡប់ទៅបញ្ជី", callback_data="back_to_recommendations")]
        ])
        
        await safe_edit_message(query, contact_text, keyboard)
        
    except Exception as e:
        logger.error(f"Error in show_contact_info: {e}")
        await safe_edit_message(
            query,
            "❌ សូមអភ័យទោស! មានបញ្ហាក្នុងការបង្ហាញ។ សូមព្យាយាមម្តងទៀត។"
        )
