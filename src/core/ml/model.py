"""
EduGuideBot v3 ML Model
RandomForest-based recommendation model for university majors
"""

import logging
import numpy as np
from typing import Dict, List, Any, Tuple
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import StandardScaler
import joblib
from pathlib import Path

logger = logging.getLogger(__name__)

# Model configuration
MODEL_CONFIG = {
    'n_estimators': 150,
    'max_depth': 10,
    'random_state': 42,
    'n_jobs': -1
}

class MLRecommender:
    """ML-based recommender using RandomForest."""
    
    def __init__(self):
        self.model = None
        self.scaler = None
        self.feature_names = None
        self.is_trained = False
        
    def train_model(self, training_data: List[Dict[str, Any]]) -> bool:
        """
        Train the RandomForest model on synthetic data.
        
        Args:
            training_data: List of training examples
            
        Returns:
            bool: True if training successful
        """
        try:
            if not training_data:
                logger.warning("No training data provided, using synthetic data")
                training_data = self._generate_synthetic_data()
            
            # Prepare features and targets
            X, y = self._prepare_training_data(training_data)
            
            if len(X) == 0:
                logger.error("No valid training data")
                return False
            
            # Initialize and train model
            self.model = RandomForestRegressor(**MODEL_CONFIG)
            self.scaler = StandardScaler()
            
            # Scale features
            X_scaled = self.scaler.fit_transform(X)
            
            # Train model
            self.model.fit(X_scaled, y)
            
            self.is_trained = True
            logger.info(f"ML model trained successfully with {len(X)} samples")
            return True
            
        except Exception as e:
            logger.error(f"Error training ML model: {e}")
            return False
    
    def predict_score(self, user_answers: Dict[int, Dict], major_data: Dict[str, Any]) -> float:
        """
        Predict compatibility score for a major.
        
        Args:
            user_answers: User assessment answers
            major_data: Major information
            
        Returns:
            float: Predicted score between 0 and 1
        """
        try:
            if not self.is_trained:
                # Train with synthetic data if not trained
                self.train_model([])
            
            # Extract features
            features = self._extract_features(user_answers, major_data)
            
            if not features:
                return 0.5  # Default score
            
            # Scale features
            features_array = np.array([features])
            features_scaled = self.scaler.transform(features_array)
            
            # Predict
            prediction = self.model.predict(features_scaled)[0]
            
            # Ensure score is between 0 and 1
            return max(0.0, min(1.0, prediction))
            
        except Exception as e:
            logger.error(f"Error predicting ML score: {e}")
            return 0.5
    
    def _extract_features(self, user_answers: Dict[int, Dict], major_data: Dict[str, Any]) -> List[float]:
        """Extract numerical features from user answers and major data."""
        try:
            features = []
            
            # User preference features (16 questions)
            for i in range(16):
                if i in user_answers:
                    features.append(float(user_answers[i]['answer_index']))
                else:
                    features.append(0.0)  # Default value
            
            # Major features
            features.extend([
                float(major_data.get('tuition_fees_usd', 1000)) / 1000.0,  # Normalized fees
                float(major_data.get('employment_rate', 50)) / 100.0,       # Normalized employment rate
                float(major_data.get('duration_years', 4)) / 6.0,           # Normalized duration
                1.0 if 'phnom penh' in major_data.get('city', '').lower() else 0.0,  # Location feature
                1.0 if 'engineering' in major_data.get('major_name_en', '').lower() else 0.0,  # Field feature
            ])
            
            return features
            
        except Exception as e:
            logger.error(f"Error extracting features: {e}")
            return []
    
    def _prepare_training_data(self, training_data: List[Dict[str, Any]]) -> Tuple[List[List[float]], List[float]]:
        """Prepare training data for the model."""
        try:
            X = []
            y = []
            
            for example in training_data:
                user_answers = example.get('user_answers', {})
                major_data = example.get('major_data', {})
                score = example.get('score', 0.5)
                
                features = self._extract_features(user_answers, major_data)
                if features:
                    X.append(features)
                    y.append(score)
            
            return X, y
            
        except Exception as e:
            logger.error(f"Error preparing training data: {e}")
            return [], []
    
    def _generate_synthetic_data(self) -> List[Dict[str, Any]]:
        """Generate synthetic training data for the model."""
        try:
            synthetic_data = []
            
            # Generate 1000 synthetic examples
            for _ in range(1000):
                # Random user answers
                user_answers = {}
                for i in range(16):
                    user_answers[i] = {
                        'answer_index': np.random.randint(0, 4),
                        'answer_text': f'answer_{np.random.randint(0, 4)}'
                    }
                
                # Random major data
                major_data = {
                    'major_name_en': np.random.choice(['Computer Science', 'Business', 'Engineering', 'Medicine']),
                    'tuition_fees_usd': np.random.uniform(500, 3000),
                    'employment_rate': np.random.uniform(40, 95),
                    'duration_years': np.random.choice([3, 4, 5, 6]),
                    'city': np.random.choice(['Phnom Penh', 'Siem Reap', 'Battambang'])
                }
                
                # Generate synthetic score based on compatibility
                score = self._calculate_synthetic_score(user_answers, major_data)
                
                synthetic_data.append({
                    'user_answers': user_answers,
                    'major_data': major_data,
                    'score': score
                })
            
            logger.info(f"Generated {len(synthetic_data)} synthetic training examples")
            return synthetic_data
            
        except Exception as e:
            logger.error(f"Error generating synthetic data: {e}")
            return []
    
    def _calculate_synthetic_score(self, user_answers: Dict[int, Dict], major_data: Dict[str, Any]) -> float:
        """Calculate synthetic compatibility score for training."""
        try:
            score = 0.5  # Base score
            
            # Location preference (question 2)
            if 2 in user_answers:
                location_pref = user_answers[2]['answer_index']
                major_city = major_data.get('city', '').lower()
                
                if location_pref == 0 and 'phnom penh' in major_city:
                    score += 0.2
                elif location_pref == 1 and 'siem reap' in major_city:
                    score += 0.2
                elif location_pref == 2 and 'battambang' in major_city:
                    score += 0.2
            
            # Budget preference (question 3)
            if 3 in user_answers:
                budget_pref = user_answers[3]['answer_index']
                major_fees = major_data.get('tuition_fees_usd', 1000)
                
                if budget_pref == 0 and major_fees < 500:
                    score += 0.15
                elif budget_pref == 1 and 500 <= major_fees <= 1000:
                    score += 0.15
                elif budget_pref == 2 and 1000 <= major_fees <= 2000:
                    score += 0.15
                elif budget_pref == 3 and major_fees > 2000:
                    score += 0.15
            
            # Interest field (question 0)
            if 0 in user_answers:
                interest = user_answers[0]['answer_index']
                major_name = major_data.get('major_name_en', '').lower()
                
                if interest == 2 and 'computer' in major_name:  # Computer Science
                    score += 0.2
                elif interest == 1 and any(field in major_name for field in ['business', 'management']):
                    score += 0.2
                elif interest == 0 and 'engineering' in major_name:
                    score += 0.2
                elif interest == 3 and 'medicine' in major_name:
                    score += 0.2
            
            # Add some randomness
            score += np.random.normal(0, 0.1)
            
            # Ensure score is between 0 and 1
            return max(0.0, min(1.0, score))
            
        except Exception as e:
            logger.error(f"Error calculating synthetic score: {e}")
            return 0.5
    
    def save_model(self, filepath: str) -> bool:
        """Save trained model to file."""
        try:
            if not self.is_trained:
                logger.error("Cannot save untrained model")
                return False
            
            model_data = {
                'model': self.model,
                'scaler': self.scaler,
                'feature_names': self.feature_names,
                'config': MODEL_CONFIG
            }
            
            joblib.dump(model_data, filepath)
            logger.info(f"Model saved to {filepath}")
            return True
            
        except Exception as e:
            logger.error(f"Error saving model: {e}")
            return False
    
    def load_model(self, filepath: str) -> bool:
        """Load trained model from file."""
        try:
            if not Path(filepath).exists():
                logger.warning(f"Model file not found: {filepath}")
                return False
            
            model_data = joblib.load(filepath)
            
            self.model = model_data['model']
            self.scaler = model_data['scaler']
            self.feature_names = model_data.get('feature_names')
            self.is_trained = True
            
            logger.info(f"Model loaded from {filepath}")
            return True
            
        except Exception as e:
            logger.error(f"Error loading model: {e}")
            return False


# Global ML recommender instance
ml_recommender = MLRecommender()
